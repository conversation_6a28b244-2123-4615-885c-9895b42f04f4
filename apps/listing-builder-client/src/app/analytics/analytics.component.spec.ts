import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { AnalyticsComponent, InsightsTab } from './analytics.component';
import { BingInsightsService } from '../bing-insights/bing-insights.service';
import { ListingSyncService } from '@vendasta/local-seo';
import { ListingProductsApiService } from '@vendasta/listing-products';
import { AccountGroupApiService } from '@vendasta/account-group';
import { AppPartnerService } from '@galaxy/marketplace-apps';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

describe('AnalyticsComponent', () => {
  let component: AnalyticsComponent;
  let fixture: ComponentFixture<AnalyticsComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: jasmine.SpyObj<ActivatedRoute>;

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const activatedRouteSpy = jasmine.createSpyObj('ActivatedRoute', [], {
      snapshot: {
        routeConfig: { path: 'google' },
        firstChild: null
      }
    });

    await TestBed.configureTestingModule({
      declarations: [AnalyticsComponent],
      providers: [
        { provide: Router, useValue: routerSpy },
        { provide: ActivatedRoute, useValue: activatedRouteSpy },
        { provide: 'AGIDTOKEN', useValue: of('test-agid') },
        { provide: BingInsightsService, useValue: { isBingDataAvailable$: of(false) } },
        { provide: ListingSyncService, useValue: { syncData$: of([]) } },
        { provide: ListingProductsApiService, useValue: {} },
        { provide: AccountGroupApiService, useValue: {} },
        { provide: AppPartnerService, useValue: {} },
        { provide: ProductAnalyticsService, useValue: { trackEvent: jasmine.createSpy() } },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AnalyticsComponent);
    component = fixture.componentInstance;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockActivatedRoute = TestBed.inject(ActivatedRoute) as jasmine.SpyObj<ActivatedRoute>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set activeTab to Google when route is google', () => {
    mockActivatedRoute.snapshot = {
      routeConfig: { path: 'google' },
      firstChild: null
    } as any;

    component['updateActiveTabFromRoute']();
    
    expect(component.activeTab).toBe(InsightsTab.Google);
  });

  it('should set activeTab to Bing when route is bing', () => {
    mockActivatedRoute.snapshot = {
      routeConfig: { path: 'bing' },
      firstChild: null
    } as any;

    component['updateActiveTabFromRoute']();
    
    expect(component.activeTab).toBe(InsightsTab.Bing);
  });

  it('should navigate to google route when switching to Google tab', () => {
    component.onTabSwitch(InsightsTab.Google);
    
    expect(mockRouter.navigate).toHaveBeenCalledWith(['../analytics', 'google'], { relativeTo: mockActivatedRoute });
  });

  it('should navigate to bing route when switching to Bing tab', () => {
    component.onTabSwitch(InsightsTab.Bing);
    
    expect(mockRouter.navigate).toHaveBeenCalledWith(['../analytics', 'bing'], { relativeTo: mockActivatedRoute });
  });

  it('should return true for isActiveTabGoogle when activeTab is Google', () => {
    component.activeTab = InsightsTab.Google;
    
    expect(component.isActiveTabGoogle()).toBe(true);
  });

  it('should return false for isActiveTabGoogle when activeTab is Bing', () => {
    component.activeTab = InsightsTab.Bing;
    
    expect(component.isActiveTabGoogle()).toBe(false);
  });
});
