<app-page [pageTitle]="pageTitleKey$ | async | translate">
  <mat-tab-group
    mat-stretch-tabs="false"
    mat-align-tabs="start"
    [selectedIndex]="activeTab"
    (selectedIndexChange)="onTabSwitch($event)"
  >
    <mat-tab>
      <ng-template mat-tab-label>
        <span class="tab-label-spacer"></span>
        <img [src]="googleIconUrl" alt="Google Icon" width="24" height="24" class="tab-icon" />
        <span class="tab-label-text">{{ 'GOOGLE_INSIGHTS.TITLE' | translate }}</span>
        <span class="tab-label-extra-spacer"></span>
      </ng-template>
    </mat-tab>
    @if (upgradeToProEnabled$ | async) {
      <mat-tab>
        <ng-template mat-tab-label>
          <span class="tab-label-spacer"></span>
          <img [src]="bingIconUrl" alt="Bing Icon" width="24" height="24" class="tab-icon" />
          <span class="tab-label-text">{{ 'BING_INSIGHTS.TITLE' | translate }}</span>
          <span class="tab-label-extra-spacer"></span>
        </ng-template>
      </mat-tab>
    }
  </mat-tab-group>

  <!-- Debug information -->
  <div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc;">
    <strong>Debug Info:</strong><br>
    Active Tab: {{ activeTab }}<br>
    Is Active Tab Google: {{ isActiveTabGoogle() }}<br>
    Is Free Edition: {{ isFreeEdition$ | async }}<br>
    Upgrade To Pro Enabled: {{ upgradeToProEnabled$ | async }}<br>
    Is Bing Data Available: {{ isBingDataAvailable$ | async }}<br>
    Is Bing Sync Enable: {{ isBingSyncEnable$ | async }}
  </div>

  @if (isActiveTabGoogle()) {
    <br />
    <div style="border: 2px solid green; padding: 10px;">
      <strong>GOOGLE CONTENT SECTION</strong>
      <app-google-insights></app-google-insights>
    </div>
  } @else {
    <br />
    <div style="border: 2px solid blue; padding: 10px;">
      <strong>BING CONTENT SECTION</strong>
      @if (isFreeEdition$ | async) {
        <div style="background: yellow; padding: 5px;">Showing Bing Empty States (Free Edition)</div>
        <app-bing-empty-states
          [upgradeToProEnabled]="upgradeToProEnabled$ | async"
          [isStanderdUser]="true"
          [isBingSyncEnable$]="isBingSyncEnable$"
          [isBingListingAvailable$]="isBingListingAvailable$"
        ></app-bing-empty-states>
      } @else {
        @if ((isBingDataAvailable$ | async) && (isBingSyncEnable$ | async)) {
          <div style="background: lightgreen; padding: 5px;">Showing Bing Insights</div>
          <app-bing-insights></app-bing-insights>
        } @else {
          <div style="background: orange; padding: 5px;">Showing Bing Empty States (Pro Edition)</div>
          <app-bing-empty-states
            [upgradeToProEnabled]="upgradeToProEnabled$ | async"
            [isStanderdUser]="false"
            [isBingSyncEnable$]="isBingSyncEnable$"
            [isBingListingAvailable$]="isBingListingAvailable$"
          ></app-bing-empty-states>
        }
      }
    </div>
  }
</app-page>
